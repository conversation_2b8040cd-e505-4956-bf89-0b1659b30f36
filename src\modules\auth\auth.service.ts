import {
  Injectable,
  UnauthorizedException,
  ConflictException,
  BadRequestException,
  NotFoundException,
  ForbiddenException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { JwtService } from '@nestjs/jwt';
import { Model, Types } from 'mongoose';
import * as bcrypt from 'bcryptjs';
import {
  CreateAuthDto,
  UpdateAuthDto,
  LoginDto,
  DeactivateAccountDto,
  CreateManagerDto,
} from './dto/auth.dto';
import { CreateIncentiveDto } from './dto/incentive.dto';
import { Auth } from './entities/auth.entity';
import { ReferralTree } from './entities/referral-tree.entity';
import { Incentive } from './entities/incentive.entity';
import { CreditDebitHistory } from '../profit/entities/credit-debit-history';
import { generateReferrals } from 'src/utils/helper';
import { MailService } from 'src/utils/sendMail';
import axios from 'axios';
import { ConfigService } from '@nestjs/config';
import { response } from 'express';
import { NotificationsService } from '../notifications/notifications.service';
import { TempReferal } from './entities/tempreferral';


function generateVerificationCode(length = 4): string {
  const chars =
    'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let code = '';
  for (let i = 0; i < length; i++) {
    code += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return code;
}

@Injectable()
export class AuthService {
  constructor(
    @InjectModel(Auth.name) private authModel: Model<Auth>,
    @InjectModel(ReferralTree.name)
    private referralTreeModel: Model<ReferralTree>,
    @InjectModel(Incentive.name) private incentiveModel: Model<Incentive>,
    @InjectModel(CreditDebitHistory.name)
    private creditDebitHistoryModel: Model<CreditDebitHistory>,
    private jwtService: JwtService,
    private configService: ConfigService,
    private mailService: MailService,
    private notificationsService: NotificationsService,
    @InjectModel(TempReferal.name)
    private tempReferalModel: Model<TempReferal>,
  ) {
    this.seedAdmin();
  }

  async seedAdmin(): Promise<void> {
    try {
      // Check if admin already exists
      const existingAdmin = await this.authModel.findOne({
        email: '<EMAIL>',
        role: 'admin',
      });

      if (!existingAdmin) {
        console.log('🔧 Creating default admin user...');

        // Hash admin password
        const saltRounds = 10;
        const hashedPassword = await bcrypt.hash('admin@traderewards1@#%', saltRounds);
        const referralCode = generateVerificationCode();

        const adminUser = new this.authModel({
          email: '<EMAIL>',
          password: hashedPassword,
          firstName: 'Admin',
          lastName: 'User',
          role: 'admin',
          status: 'active',
          isEmailVerified: true,
          loginType: 'email',
          referralCode,
          refferedBy: null,
        });

        await adminUser.save();
        console.log('✅ Default admin user created successfully!');
        console.log('📧 Email: <EMAIL>');
        console.log('🔑 Password: admin123');
      } else {
        console.log('✅ Admin user already exists');
      }
    } catch (error) {
      console.error('❌ Error creating admin user:', error.message);
    }
  }

  async verifyCaptcha(token: string): Promise<void> {
    const secret = this.configService.get<string>('RECAPTCHA_SECRET');
    console.log('i am secret', secret);

    if (!secret) {
      throw new Error('RECAPTCHA_SECRET is not defined');
    }
    const url = 'https://www.google.com/recaptcha/api/siteverify';

    const params = new URLSearchParams();

    params.append('secret', secret);
    params.append('response', token);

    const { data } = await axios.post(url, params);

    if (!data.success || (data.score !== undefined && data.score < 0.5)) {
      throw new UnauthorizedException('CAPTCHA verification failed');
    }
  }

  async create(createAuthDto: CreateAuthDto) {
    // Check if user already exists
    const existingUser = await this.authModel.findOne({
      email: createAuthDto.email,
    });
    if (existingUser && createAuthDto.loginType == 'email') {
      console.log('❌ User already exists:', createAuthDto.email);
      throw new ConflictException('User with this email already exists');
    }
    let referralCode = generateVerificationCode();
    let refferedUser: any = null;

    // If referral code is provided, find that user
    if (createAuthDto.refferedBy) {
      refferedUser = await this.authModel.findOne({
        referralCode: createAuthDto.refferedBy,
      });

      if (!refferedUser) {
        console.log('❌ Reffered User not found:', createAuthDto.refferedBy);
        throw new BadRequestException('Reffered User not found');
      }

      if(refferedUser.isEmailVerified==false){
        console.log('❌ Reffered User not verified:', createAuthDto.refferedBy);
        throw new BadRequestException('Reffered User not verified');
      }
    }
    // If no referral code provided, use default parent from env
    else {
      const defaultParentId =
        this.configService.get<string>('referral_parrent');
      if (defaultParentId) {
        refferedUser = await this.authModel.findById(defaultParentId);
        if (refferedUser) {
          console.log('✅ Using default parent referral:', refferedUser.email);
          createAuthDto.refferedBy = refferedUser.referralCode;
        } else {
          console.log('❌ Default parent referral not found:', defaultParentId);
        }
      }
    }

    if (createAuthDto.loginType == 'email') {
      if (!createAuthDto.password) {
        console.log('❌ Password is required');
        throw new UnauthorizedException('Password is required');
      }

      // Hash password
      const saltRounds = 10;
      const hashedPassword = await bcrypt.hash(
        createAuthDto.password,
        saltRounds,
      );
      const verificationCode = generateVerificationCode();

      const createdAuth = new this.authModel({
        ...createAuthDto,
        password: hashedPassword,
        verificationCode,
        referralCode,
        refferedBy: null, // Save as null initially
      });

      const result = await createdAuth.save();
      console.log('✅ User created successfully:', result._id);

      // Save temp referral data instead of processing immediately
      if (createAuthDto.refferedBy && refferedUser) {
        console.log('💾 Saving temp referral data for:', result.email);
        
        const tempReferral = new this.tempReferalModel({
          userId: result._id.toString(),
          referredBy: createAuthDto.refferedBy,
        });

        await tempReferral.save();
        console.log('✅ Temp referral data saved successfully');
      }

      // Send verification email
      try {
        await this.mailService.sendVerificationCodeEmail(
          result.email,
          verificationCode,
          referralCode,
        );
        console.log('✅ Verification email sent to:', result.email);
      } catch (error) {
        console.error('❌ Failed to send verification email:', error);
      }

      // Remove password from response
      const { password, ...userWithoutPassword } = result.toObject();
      return userWithoutPassword;
    }
    // Keep Google login unchanged as it processes referrals immediately
    else if (createAuthDto.loginType == 'google') {
      const user = await this.authModel.findOne({ email: createAuthDto.email });
      if (user) {
        const payload = {
          email: user.email,
          sub: user._id,
          role: user.role,
        };

        // Generate access token (3 hours)
        const accessToken = this.jwtService.sign(payload);

        // Generate refresh token (7 days)
        const refreshToken = this.jwtService.sign(payload, { expiresIn: '7d' });

        return {
          access_token: accessToken,
          refresh_token: refreshToken,
          expires_in: 3 * 60 * 60,
          user: {
            id: user._id,
            email: user.email,
            firstName: user.firstName,
            lastName: user.lastName,
            role: user.role,
            referralCode: user.referralCode,

          },
        };
      }

      const createdAuth = new this.authModel({
        ...createAuthDto,
        referralCode,
        isEmailVerified: true,
      });
      await createdAuth.save();
      console.log('✅ User created successfully:', createdAuth._id);
      console.log('outside function', refferedUser);

      if (createAuthDto.refferedBy && refferedUser) {
        console.log('Reached here', refferedUser);

        await generateReferrals(
          createdAuth,
          refferedUser,
          this.referralTreeModel,
          this.notificationsService,
        );
        await refferedUser.save();
      }

      const payload = {
        email: createdAuth.email,
        sub: createdAuth._id,
        role: createdAuth.role,
      };

      // Generate access token (3 hours)
      const accessToken = this.jwtService.sign(payload);

      // Generate refresh token (7 days)
      const refreshToken = this.jwtService.sign(payload, { expiresIn: '7d' });

      // Save refresh token in database
      await this.authModel.findByIdAndUpdate(createdAuth._id, { refreshToken });
      console.log('🔄 Refresh token saved for:', createdAuth.email);

      return {
        access_token: accessToken,
        refresh_token: refreshToken,
        expires_in: 3 * 60 * 60,
        user: {
          id: createdAuth._id,
          email: createdAuth.email,
          firstName: createdAuth.firstName,
          lastName: createdAuth.lastName,
          role: createdAuth.role,
          referralCode: createdAuth.referralCode,
        },
      };
    } else {
      return { message: 'Login type not supported' };
    }
  }

  async findAll(userId: string) {
    const user = await this.authModel.findById(userId).exec();
    if (!user) {
      throw new NotFoundException('User not found');
    }
    if (user.role != 'admin') {
      throw new UnauthorizedException('Unauthorized');
    }
    console.log('🔍 Fetching all users');
    const users = await this.authModel
      .find({ 
        role: { $ne: 'admin' },
        isEmailVerified: true 
      })
      .select('-password -refreshToken -verificationCode')
      .sort({ createdAt: -1 })
      .exec();
    console.log(`📊 Found ${users.length} users`);

    // Calculate team count for each user
    const usersWithTeamCount = users.map((user) => {
      // Count level1, level2, and level3 referrals from user's own fields
      const level1Count = user.level1?.length || 0;
      const level2Count = user.level2?.length || 0;
      const level3Count = user.level3?.length || 0;

      const teamCount = level1Count + level2Count + level3Count;
      const totalEarned = user.totalTradeEarnings + user.totalReferralEarnings;
      const connectedBrokers = user.connectedBrokers?.length || 0;

      return {
        ...user.toObject(),
        team: teamCount,
        totalEarned,
        connectedBrokers,
      };
    });

    return usersWithTeamCount;
  }

  async findUsersByEarnings(userId: string) {
    console.log('🔍 Fetching users ranked by total trade earnings');

    // Check if requesting user is admin
    const user = await this.authModel.findById(userId);
    if (!user) {
      throw new UnauthorizedException('User not found');
    }
    // if (user.role != 'admin') {
    //   throw new UnauthorizedException('Unauthorized');
    // }

    console.log('🔍 Fetching all users ranked by earnings');
    const users = await this.authModel
      .find({ role: { $ne: 'admin' } })
      .select('-password -refreshToken -verificationCode')
      .sort({ totalTradeEarnings: -1 })
      .exec();
    console.log(`📊 Found ${users.length} users`);

    // Calculate team count for each user
    const usersWithTeamCount = users.map((user) => {
      // Count level1, level2, and level3 referrals from user's own fields
      const level1Count = user.level1 ? user.level1.length : 0;
      const level2Count = user.level2 ? user.level2.length : 0;
      const level3Count = user.level3 ? user.level3.length : 0;

      const team = level1Count + level2Count + level3Count;

      const totalEarned = user.totalTradeEarnings + user.totalReferralEarnings;

      return {
        ...user.toObject(),
        team,
        totalEarned,
      };
    });

    const sortedUsers = usersWithTeamCount.sort(
      (a, b) => b.totalEarned - a.totalEarned,
    );

    const rankedUsers = sortedUsers.map((user, index) => ({
      ...user,
      rank: index + 1,
    }));

    console.log('✅ Users ranked successfully by total earnings');
    return rankedUsers;
  }

  async findOne(id: string) {
    console.log('🔍 Finding user by ID:', id);

    // Validate if the provided ID is a valid ObjectId
    if (!Types.ObjectId.isValid(id)) {
      console.log('❌ Invalid ObjectId format:', id);
      throw new BadRequestException('Invalid user ID format');
    }

    const user = await this.authModel.findById(id).exec();
    if (user) {
      console.log('✅ User found:', user.email);
    } else {
      console.log('❌ User not found');
    }
    return user;
  }

  async login(loginDto: LoginDto) {
    console.log('🔐 Login attempt for:', loginDto.email);

    const user = await this.authModel.findOne({ email: loginDto.email });
    if (!user) {
      console.log('❌ User not found:', loginDto.email);
      throw new UnauthorizedException('User not found!');
    }

    if (user.role == 'admin') {
      console.log('Admin found:', user.email);
      throw new UnauthorizedException('Invalid credentials');
    }

    if (user.isEmailVerified == false) {
      console.log('❌ Email not verified:', loginDto.email);
      
      // Generate new verification code and send email
      const verificationCode = generateVerificationCode();
      await this.authModel.findByIdAndUpdate(user._id, {
        verificationCode,
      });
      
      try {
        await this.mailService.sendVerificationCodeEmail(
          user.email,
          verificationCode,
          user.referralCode,
        );
        console.log('✅ Verification email sent to:', user.email);
      } catch (error) {
        console.error('❌ Failed to send verification email:', error);
      }
      
      throw new UnauthorizedException('Email not verified. Verification email sent.');
    }

    if (user.status === 'inactive') {
      throw new ForbiddenException(
        'Account is deactivated. Please contact support.',
      );
    }

    const isPasswordValid = await bcrypt.compare(
      loginDto.password,
      user.password,
    );
    if (!isPasswordValid) {
      console.log('❌ Invalid password for:', loginDto.email);
      throw new BadRequestException('Invalid credentials');
    }

    console.log('✅ Login successful for:', user.email);

    const payload = {
      email: user.email,
      sub: user._id,
      role: user.role,
    };

    // Generate access token (6 hours)
    const accessToken = this.jwtService.sign(payload, { expiresIn: '6h' });

    // Generate refresh token (7 days)
    const refreshToken = this.jwtService.sign(payload, { expiresIn: '7d' });

    // Save refresh token in database
    await this.authModel.findByIdAndUpdate(user._id, { refreshToken });
    console.log('🔄 Refresh token saved for:', user.email);

    return {
      access_token: accessToken,
      refresh_token: refreshToken,
      expires_in: 6 * 60 * 60, // 6 hours in seconds
      user: {
        id: user._id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role,
        referralCode: user.referralCode,
      },
    };
  }

  async adminLogin(loginDto: LoginDto) {
    console.log('🔐 Admin login attempt for:', loginDto.email);

    const user = await this.authModel.findOne({
      email: loginDto.email,
      role: 'admin',
    });

    if (!user) {
      console.log('❌ Admin not found:', loginDto.email);
      throw new UnauthorizedException('Invalid admin credentials');
    }

    if (user.role != 'admin') {
      console.log('Admin found:', user.email);
      throw new UnauthorizedException('Invalid admin credentials');
    }

    if (user.status === 'inactive') {
      throw new ForbiddenException('Admin account is deactivated');
    }

    const isPasswordValid = await bcrypt.compare(
      loginDto.password,
      user.password,
    );

    if (!isPasswordValid) {
      console.log('❌ Invalid password for admin:', loginDto.email);
      throw new UnauthorizedException('Invalid admin credentials');
    }

    console.log('✅ Admin login successful:', user.email);

    const payload = {
      email: user.email,
      sub: user._id,
      role: user.role,
    };

    // Generate access token (30 minutes for admin)
    const accessToken = this.jwtService.sign(payload, { expiresIn: '30m' });

    // Generate refresh token (7 days)
    const refreshToken = this.jwtService.sign(payload, { expiresIn: '7d' });

    // Save refresh token in database
    await this.authModel.findByIdAndUpdate(user._id, { refreshToken });
    console.log('🔄 Refresh token saved for admin:', user.email);

    return {
      access_token: accessToken,
      refresh_token: refreshToken,
      expires_in: 30 * 60, // 30 minutes in seconds
      user: {
        id: user._id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role,
      },
    };
  }

  async refreshToken(refreshToken: string) {
    console.log('🔄 Refresh token attempt');

    try {
      // Verify refresh token
      const payload = this.jwtService.verify(refreshToken);

      // Find user with this refresh token
      const user = await this.authModel.findOne({
        _id: payload.sub,
        refreshToken: refreshToken,
      });

      if (!user) {
        console.log('❌ Invalid refresh token');
        throw new UnauthorizedException('Invalid refresh token');
      }

      console.log('✅ Refresh token valid for:', user.email);

      // Generate new access token
      const newPayload = {
        email: user.email,
        sub: user._id,
        role: user.role,
      };

      const newAccessToken = this.jwtService.sign(newPayload);

      // Generate new refresh token
      const newRefreshToken = this.jwtService.sign(newPayload, {
        expiresIn: '7d',
      });

      // Update refresh token in database
      await this.authModel.findByIdAndUpdate(user._id, {
        refreshToken: newRefreshToken,
      });
      console.log('🔄 New tokens generated for:', user.email);

      return {
        access_token: newAccessToken,
        refresh_token: newRefreshToken,
        expires_in: 3 * 60 * 60, // 3 hours in seconds
        user: {
          id: user._id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          role: user.role,
        },
      };
    } catch (error) {
      console.log('❌ Refresh token verification failed:', error.message);
      throw new UnauthorizedException('Invalid or expired refresh token');
    }
  }

  async logout(userId: string) {
    console.log('🚪 Logout for user:', userId);

    // Remove refresh token from database
    await this.authModel.findByIdAndUpdate(userId, { refreshToken: null });
    console.log('✅ User logged out successfully');

    return { message: 'Logged out successfully' };
  }

  async verifyEmail(code: string, email: string) {
    console.log('_VERIFYING_EMAIL_ for user:', code);
    console.log('code for user:', code);
    const user = await this.authModel.findOne({ email }).exec();
    if (!user) {
      console.log('❌ User not found invalid code');
      throw new UnauthorizedException('invalid code');
    }

    if (user.verificationCode != code) {
      console.log('❌ Invalid code');
      throw new UnauthorizedException('invalid code');
    }

    await this.authModel.findByIdAndUpdate(user._id, {
      isEmailVerified: true,
      verificationCode: null,
    });
    console.log('✅ Email verified successfully');

    // Process temp referral data after email verification
    const tempReferral = await this.tempReferalModel.findOne({
      userId: user._id.toString(),
    });

    if (tempReferral) {
      console.log('🔄 Processing temp referral data for:', user.email);
      
      try {
        // Get the referred user from temp data
        const refferedUser = await this.authModel.findOne({referralCode:tempReferral.referredBy});
        
        if (refferedUser) {
          // Update user's refferedBy field
          await this.authModel.findByIdAndUpdate(user._id, {
            refferedBy: tempReferral.referredBy,
          });
          console.log('✅ User refferedBy field updated');

          await generateReferrals(
            user,
            refferedUser,
            this.referralTreeModel,
            this.notificationsService,
          );
          await refferedUser.save();
          
          // Mark temp referral as processed
          await this.tempReferalModel.findByIdAndUpdate(tempReferral._id, {
            processed: true,
          });
          
          console.log('✅ Referral processing completed successfully');
        } else {
          console.log('❌ Referred user not found during processing');
        }
      } catch (error) {
        console.error('❌ Error processing referral:', error);
      }
    }

    // Generate tokens after successful verification
    const payload = {
      email: user.email,
      sub: user._id,
      role: user.role,
    };

    // Generate access token (3 hours)
    const accessToken = this.jwtService.sign(payload);

    // Generate refresh token (7 days)
    const refreshToken = this.jwtService.sign(payload, { expiresIn: '7d' });

    // Save refresh token in database
    await this.authModel.findByIdAndUpdate(user._id, { refreshToken });
    console.log('🔄 Refresh token saved for:', user.email);

    return {
      message: 'Email verified successfully',
      access_token: accessToken,
      refresh_token: refreshToken,
      expires_in: 3 * 60 * 60, // 3 hours in seconds
      user: {
        id: user._id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role,
        referralCode: user.referralCode,
      },
    };
  }

  async forgetPassword(email: string) {
    console.log('_FORGET_PASSWORD_ for user:', email);
    const user = await this.authModel.findOne({ email }).exec();
    if (!user) {
      console.log('❌ User not found invalid code');
      throw new UnauthorizedException('invalid code');
    }
    const verificationCode = generateVerificationCode();
    await this.authModel.findByIdAndUpdate(user._id, {
      verificationCode,
      resetPassword: true,
    });
    await this.mailService.sendResetPasswordEmail(email, verificationCode);
    console.log('✅ Verification code sent successfully');
    return { message: 'Verification code sent successfully' };
  }

  async verifyResetPassword(code: string, email: string) {
    console.log('_VERIFY_RESET_PASSWORD_ for user:', email);
    const user = await this.authModel.findOne({ email }).exec();
    if (!user) {
      console.log('❌ User not found invalid code');
      throw new UnauthorizedException('invalid code');
    }
    if (user.verificationCode != code) {
      console.log('❌ Invalid code');
      throw new UnauthorizedException('invalid code');
    }
    if (!user.resetPassword) {
      console.log('❌ Reset password not requested');
      throw new UnauthorizedException('Reset password not requested');
    }
    console.log('✅ Reset password code verified successfully');
    return { message: 'Reset password code verified successfully' };
  }

  async resetPassword(code: string, email: string, password: string) {
    console.log('_RESET_PASSWORD_ for user:', email);
    const user = await this.authModel.findOne({ email }).exec();
    if (!user) {
      console.log('❌ User not found invalid code');
      throw new UnauthorizedException('invalid code');
    }
    if (user.verificationCode != code) {
      console.log('❌ Invalid code');
      throw new UnauthorizedException('invalid code');
    }
    const saltRounds = 10;
    const hashedPassword = await bcrypt.hash(password, saltRounds);
    await this.authModel.findByIdAndUpdate(user._id, {
      password: hashedPassword,
      verificationCode: null,
      resetPassword: false,
    });
    console.log('✅ Password reset successfully');
    return { message: 'Password reset successfully' };
  }

  async updateEmail(userId: string, newEmail: string) {
    const user = await this.authModel.findById(userId).exec();
    if (!user) {
      throw new NotFoundException('User not found');
    }
    if (user.email.toLowerCase() === newEmail.toLowerCase()) {
      throw new BadRequestException(
        'New email cannot be the same as current email',
      );
    }

    const existingUser = await this.authModel
      .findOne({ email: newEmail.toLowerCase() })
      .exec();
    if (existingUser && existingUser._id.toString() !== userId) {
      throw new ConflictException('Email already exists');
    }

    const verificationCode = generateVerificationCode();
    await this.authModel.findByIdAndUpdate(userId, {
      email: newEmail.toLowerCase(),
      isEmailVerified: false,
      verificationCode: verificationCode,
    });

    try {
      await this.mailService.sendVerificationCodeEmail(
        newEmail.toLowerCase(),
        verificationCode,
        user.referralCode,
      );
      console.log('Email verification sent to the updated email:', newEmail);
    } catch (error) {
      console.error('Failed to send verification email:', error);
      throw new BadRequestException('Failed to send verification email');
    }

    return {
      message:
        'Email updated successfully. Please check your new email for verification link.',
      email: newEmail.toLowerCase(),
    };
  }

  async changePassword(
    userId: number,
    oldPassword: string,
    newPassword: string,
  ) {
    const user = await this.authModel.findById(userId);
    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Check if user has a password set
    if (!user.password) {
      throw new BadRequestException(
        'User does not have a password set. As user signed up with google.',
      );
    }

    // Check if oldPassword is provided
    if (!oldPassword) {
      throw new BadRequestException('Old password is required');
    }

    const isMatch = await bcrypt.compare(oldPassword, user.password);
    if (!isMatch) {
      throw new UnauthorizedException('Old password is incorrect');
    }

    if (oldPassword == newPassword) {
      throw new BadRequestException('New password cannot be old password');
    }

    const hashedNewPassword = await bcrypt.hash(newPassword, 10);
    user.password = hashedNewPassword;

    await user.save();
    return {
      status: 200,
      message: 'Password changed successfully',
      response: user,
    };
  }

  async deactivateAccount(userId: string, reason?: string) {
    const user = await this.authModel.findById(userId);
    if (!user) {
      throw new NotFoundException('User not found');
    }

    if (user.status === 'deleted') {
      throw new BadRequestException('Account is already deactivated');
    }

    await this.authModel.findByIdAndUpdate(userId, {
      status: 'deleted',
      refreshToken: null,
    });

    return {
      status: 200,
      message: 'Account deactivated successfully',
      deactivatedAt: new Date(),
      data: user,
    };
  }

  async getUserReferrals(adminId: string, userId: string) {
    const admin = await this.authModel.findById(adminId);
    // if (!admin) {
    //   throw new NotFoundException('Admin not found');
    // }

    // if (admin.role !== 'admin') {
    //   throw new UnauthorizedException('Only admins can view user referrals');
    // }

    const targetUser = await this.authModel.findById(userId);
    if (!targetUser) {
      throw new NotFoundException('User not found');
    }

    const userWithReferrals = await this.authModel
      .findById(userId)
      .populate({
        path: 'level1',
        select:
          'firstName lastName email referralCode createdAt totalReferralEarnings totalTradeEarnings balance status level1 level2 level3 rank totalRebate',
        populate: [
          {
            path: 'level1',
            select:
              'firstName lastName email referralCode createdAt totalReferralEarnings totalTradeEarnings balance status rank totalRebate',
          },
          {
            path: 'level2',
            select:
              'firstName lastName email referralCode createdAt totalReferralEarnings totalTradeEarnings balance status rank totalRebate',
          },
          {
            path: 'level3',
            select:
              'firstName lastName email referralCode createdAt totalReferralEarnings totalTradeEarnings balance status rank totalRebate',
          },
        ],
      })
      .populate({
        path: 'level2',
        select:
          'firstName lastName email referralCode createdAt totalReferralEarnings totalTradeEarnings balance status level1 level2 level3 rank totalRebate',
        populate: [
          {
            path: 'level1',
            select:
              'firstName lastName email referralCode createdAt totalReferralEarnings totalTradeEarnings balance status rank totalRebate',
          },
          {
            path: 'level2',
            select:
              'firstName lastName email referralCode createdAt totalReferralEarnings totalTradeEarnings balance status rank totalRebate',
          },
          {
            path: 'level3',
            select:
              'firstName lastName email referralCode createdAt totalReferralEarnings totalTradeEarnings balance status rank totalRebate',
          },
        ],
      })
      .populate({
        path: 'level3',
        select:
          'firstName lastName email referralCode createdAt totalReferralEarnings totalTradeEarnings balance status level1 level2 level3 rank totalRebate',
        populate: [
          {
            path: 'level1',
            select:
              'firstName lastName email referralCode createdAt totalReferralEarnings totalTradeEarnings balance status rank totalRebate',
          },
          {
            path: 'level2',
            select:
              'firstName lastName email referralCode createdAt totalReferralEarnings totalTradeEarnings balance status rank totalRebate',
          },
          {
            path: 'level3',
            select:
              'firstName lastName email referralCode createdAt totalReferralEarnings totalTradeEarnings balance status rank totalRebate',
          },
        ],
      })
      .select(
        'firstName lastName email referralCode level1 level2 level3 referralCount, totalReferralEarnings',
      )
      .exec();

    if (!userWithReferrals) {
      throw new NotFoundException('User not found');
    }

    const level1Users = userWithReferrals.level1 as any[];
    const level2Users = userWithReferrals.level2 as any[];
    const level3Users = userWithReferrals.level3 as any[];

    // Helper function to calculate total earnings for a user and their referrals
    const calculateUserWithReferralsEarnings = (user: any) => {
      const userEarnings = user.totalReferralEarnings || 0;
      const level1Earnings = (user.level1 || []).reduce(
        (acc: number, ref: any) => acc + (ref.totalReferralEarnings || 0),
        0,
      );
      const level2Earnings = (user.level2 || []).reduce(
        (acc: number, ref: any) => acc + (ref.totalReferralEarnings || 0),
        0,
      );
      const level3Earnings = (user.level3 || []).reduce(
        (acc: number, ref: any) => acc + (ref.totalReferralEarnings || 0),
        0,
      );
      return userEarnings + level1Earnings + level2Earnings + level3Earnings;
    };

    // Helper function to count total referrals for a user
    const calculateUserTotalReferrals = (user: any) => {
      const level1Count = (user.level1 || []).length;
      const level2Count = (user.level2 || []).length;
      const level3Count = (user.level3 || []).length;
      return level1Count + level2Count + level3Count;
    };

    // Enhanced user data with nested referrals
    const enhancedLevel1Users = level1Users.map((user) => ({
      ...user.toObject(),
      nestedReferrals: {
        level1: user.level1 || [],
        level2: user.level2 || [],
        level3: user.level3 || [],
      },
      totalNestedReferrals: calculateUserTotalReferrals(user),
      totalEarningsWithReferrals: calculateUserWithReferralsEarnings(user),
      rank: user.rank || 'bronze',
      totalTradeEarnings: user.totalTradeEarnings || 0,
      totalRebate: user.totalRebate || 0,
    }));

    const enhancedLevel2Users = level2Users.map((user) => ({
      ...user.toObject(),
      nestedReferrals: {
        level1: user.level1 || [],
        level2: user.level2 || [],
        level3: user.level3 || [],
      },
      totalNestedReferrals: calculateUserTotalReferrals(user),
      totalEarningsWithReferrals: calculateUserWithReferralsEarnings(user),
      rank: user.rank || 'bronze',
      totalTradeEarnings: user.totalTradeEarnings || 0,
      totalRebate: user.totalRebate || 0,
    }));

    const enhancedLevel3Users = level3Users.map((user) => ({
      ...user.toObject(),
      nestedReferrals: {
        level1: user.level1 || [],
        level2: user.level2 || [],
        level3: user.level3 || [],
      },
      totalNestedReferrals: calculateUserTotalReferrals(user),
      totalEarningsWithReferrals: calculateUserWithReferralsEarnings(user),
      rank: user.rank || 'bronze',
      totalTradeEarnings: user.totalTradeEarnings || 0,
      totalRebate: user.totalRebate || 0,
    }));

    const level1TotalEarnings = level1Users.reduce(
      (acc, user) => acc + (user.totalReferralEarnings || 0),
      0,
    );
    const level2TotalEarnings = level2Users.reduce(
      (acc, user) => acc + (user.totalReferralEarnings || 0),
      0,
    );
    const level3TotalEarnings = level3Users.reduce(
      (acc, user) => acc + (user.totalReferralEarnings || 0),
      0,
    );

    // Calculate total nested referrals count

    const totalNestedLevel1 = enhancedLevel1Users.reduce(
      (acc, user) => acc + user.totalNestedReferrals,
      0,
    );
    const totalNestedLevel2 = enhancedLevel2Users.reduce(
      (acc, user) => acc + user.totalNestedReferrals,
      0,
    );
    const totalNestedLevel3 = enhancedLevel3Users.reduce(
      (acc, user) => acc + user.totalNestedReferrals,
      0,
    );

    // Calculate total earnings including nested referrals
    const totalEarningsWithNested =
      enhancedLevel1Users.reduce(
        (acc, user) => acc + user.totalEarningsWithReferrals,
        0,
      ) +
      enhancedLevel2Users.reduce(
        (acc, user) => acc + user.totalEarningsWithReferrals,
        0,
      ) +
      enhancedLevel3Users.reduce(
        (acc, user) => acc + user.totalEarningsWithReferrals,
        0,
      );

    const response = {
      user: {
        id: userWithReferrals._id,
        firstName: userWithReferrals.firstName,
        lastName: userWithReferrals.lastName,
        email: userWithReferrals.email,
        referralCode: userWithReferrals.referralCode,
        totalReferralCount: userWithReferrals.referralCount,
        totalReferralEarnings: userWithReferrals.totalReferralEarnings,
        rank: userWithReferrals.rank || 'bronze',
        totalTradeEarnings: userWithReferrals.totalTradeEarnings || 0,
        totalRebate: userWithReferrals.totalRebate || 0,
      },
      referrals: {
        level1: {
          count: level1Users.length,
          users: enhancedLevel1Users,
          totalEarnings: level1TotalEarnings,
          totalNestedReferrals: totalNestedLevel1,
        },
        level2: {
          count: level2Users.length,
          users: enhancedLevel2Users,
          totalEarnings: level2TotalEarnings,
          totalNestedReferrals: totalNestedLevel2,
        },
        level3: {
          count: level3Users.length,
          users: enhancedLevel3Users,
          totalEarnings: level3TotalEarnings,
          totalNestedReferrals: totalNestedLevel3,
        },
      },
      summary: {
        totalLevel1: level1Users.length,
        totalLevel2: level2Users.length,
        totalLevel3: level3Users.length,
        totalTeam: level1Users.length + level2Users.length + level3Users.length,
        totalEarningsAllLevels:
          level1TotalEarnings + level2TotalEarnings + level3TotalEarnings,
        totalNestedReferralsAllLevels:
          totalNestedLevel1 + totalNestedLevel2 + totalNestedLevel3,
        totalEarningsWithNestedReferrals: totalEarningsWithNested,
        grandTotalTeam:
          level1Users.length +
          level2Users.length +
          level3Users.length +
          (totalNestedLevel1 + totalNestedLevel2 + totalNestedLevel3),
      },
    };

    return response;
  }

  async giveIncentiveToTopUsers(
    adminId: string,
    createIncentiveDto: CreateIncentiveDto,
  ) {
    console.log('💰 Admin giving incentives to top users:', adminId);

    const admin = await this.authModel.findById(adminId);
    if (!admin) {
      throw new NotFoundException('Admin not found');
    }

    if (admin.role !== 'admin') {
      throw new UnauthorizedException('Only admins can give incentives');
    }

    const results: any[] = [];
    const errors: any[] = [];

    for (let i = 0; i < createIncentiveDto.users.length; i++) {
      const userIncentive = createIncentiveDto.users[i];

      try {
        const user = await this.authModel.findById(userIncentive.userId);
        if (!user) {
          errors.push({
            userId: userIncentive.userId,
            userName: userIncentive.userName,
            error: 'User not found',
          });
          continue;
        }

        if (user.role === 'admin') {
          errors.push({
            userId: userIncentive.userId,
            userName: userIncentive.userName,
            error: 'Cannot give incentive to admin users',
          });
          continue;
        }

        const incentive = new this.incentiveModel({
          userId: userIncentive.userId,
          userName: userIncentive.userName,
          cashback: userIncentive.cashback,
          rank: i + 1,
          description:
            createIncentiveDto.description || `Rank ${i + 1} incentive`,
        });

        await incentive.save();

        // Get user's balance before update
        const userBeforeUpdate = await this.authModel.findById(
          userIncentive.userId,
        );
        if (!userBeforeUpdate) {
          throw new NotFoundException('User not found during balance update');
        }
        const balanceBefore = userBeforeUpdate.balance || 0;

        await this.authModel.findByIdAndUpdate(userIncentive.userId, {
          $inc: { 
            balance: userIncentive.cashback,
            totalIncentives: userIncentive.cashback 
          },
        });

        // Get user's balance after update
        const userAfterUpdate = await this.authModel.findById(
          userIncentive.userId,
        );
        if (!userAfterUpdate) {
          throw new NotFoundException('User not found after balance update');
        }
        const balanceAfter = userAfterUpdate.balance || 0;

        // Create credit-debit history record
        const historyRecord = new this.creditDebitHistoryModel({
          userId: userIncentive.userId,
          type: 'credit',
          amount: userIncentive.cashback,
          firstname: user.firstName,
          source: 'incentive',
          currency: 'usdt',
          description: `Admin incentive - Rank ${i + 1}: ${createIncentiveDto.description || `Rank ${i + 1} incentive`}`,
          balanceBefore: balanceBefore,
          balanceAfter: balanceAfter,
          from:"admin",
          to: user.firstName + " " + user.lastName,
        });

        await historyRecord.save();

        // Create cashback notification for the user
        try {
          await this.notificationsService.createCashbackNotification(
            userIncentive.userId,
            userIncentive.cashback
          );
          console.log(`✅ Incentive notification sent to user: ${userIncentive.userName}`);
        } catch (error) {
          console.error(`❌ Failed to send incentive notification to ${userIncentive.userName}:`, error);
        }

        results.push({
          userId: userIncentive.userId,
          userName: userIncentive.userName,
          cashback: userIncentive.cashback,
          rank: i + 1,
          status: 'success',
          incentiveId: incentive._id,
        });

        console.log(
          ` Incentive given to user ${userIncentive.userName}: $${userIncentive.cashback}`,
        );
      } catch (error) {
        console.error(
          `Error giving incentive to user ${userIncentive.userName}:`,
          error,
        );
        errors.push({
          userId: userIncentive.userId,
          userName: userIncentive.userName,
          error: error.message || 'Unknown error occurred',
        });
      }
    }

    const response = {
      message: 'Incentive distribution completed',
      totalProcessed: createIncentiveDto.users.length,
      successful: results.length,
      failed: errors.length,
      results,
      errors,
    };

    console.log('Incentive distribution completed:', response);
    return response;
  }

  async getAllIncentives(adminId: string) {
    console.log('📋 Admin getting all incentives:', adminId);

    // Check if requester is admin
    const admin = await this.authModel.findById(adminId);
    if (!admin) {
      throw new NotFoundException('Admin not found');
    }

    if (admin.role !== 'admin') {
      throw new UnauthorizedException('Only admins can view all incentives');
    }

    const incentives = await this.incentiveModel
      .find()
      .populate('userId', 'firstName lastName email')
      .populate('givenBy', 'firstName lastName email')
      .sort({ createdAt: -1 })
      .exec();

    return incentives;
  }

  async getUserIncentives(userId: string) {
    console.log('📋 Getting incentives for user:', userId);

    const incentives = await this.incentiveModel
      .find({ userId })
      .populate('givenBy', 'firstName lastName email')
      .sort({ createdAt: -1 })
      .exec();

    return incentives;
  }

  async lockAndUnlockUser(adminId: string, userId: string) {
    const admin = await this.authModel.findById(adminId);
    if (!admin) {
      throw new NotFoundException('Admin not found');
    }

    if (admin.role !== 'admin') {
      throw new UnauthorizedException('Only admins can toggle user status');
    }

    const user = await this.authModel.findById(userId);
    if (!user) {
      throw new NotFoundException('User not found');
    }

    if (user.role === 'admin') {
      throw new BadRequestException('Cannot toggle status for admin users');
    }

    let newStatus = user.status;

    if (user.status === 'active') {
      newStatus = 'locked';
    } else if (user.status === 'locked' || user.status === 'deleted') {
      newStatus = 'active';
    }

    const updatedUser = await this.authModel
      .findByIdAndUpdate(userId, { status: newStatus }, { new: true })
      .select('-password -refreshToken -verificationCode');

    const action = newStatus === 'active' ? 'unlocked' : 'locked';
    console.log(`✅ User ${action} successfully:`, user.email);

    // Create account status notification
    try {
      await this.notificationsService.createAccountStatusNotification(
        userId,
        action as 'locked' | 'unlocked'
      );
      console.log(`✅ Account ${action} notification sent to user:`, user.email);
    } catch (error) {
      console.error(`❌ Failed to send ${action} notification:`, error);
    }

    return {
      status: 200,
      message: `User ${action} successfully`,
      action: action,
      statusUpdatedTo: newStatus,
      updatedAt: new Date(),
      data: updatedUser,
    };
  }

  async getUserStatus(userId: string) {
    const user = await this.authModel
      .findById(userId)
      .select('status email firstName lastName ');
    if (!user) {
      throw new NotFoundException('User not found');
    }

    return {
      status: 200,
      message: 'User status retrieved successfully',
      data: {
        userId: user._id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        status: user.status,
      },
    };
  }

  async getLast7DaysUsers(adminId: string) {
    const admin = await this.authModel.findById(adminId);
    if (!admin) {
      throw new UnauthorizedException('Admin not found');
    }
    if (admin.role !== 'admin') {
      throw new UnauthorizedException('Only admins can access this endpoint');
    }

    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

    const users = await this.authModel
      .find({
        role: { $ne: 'admin' },
        createdAt: { $gte: sevenDaysAgo },
      })
      .select('-password -refreshToken -verificationCode')
      .sort({ createdAt: -1 })
      .exec();

    console.log(`✅ Found ${users.length} users from last 7 days`);

    return {
      message: 'Last 7 days users retrieved successfully',
      totalUsers: users.length,
      dateRange: {
        from: sevenDaysAgo,
        to: new Date(),
      },
      data: users,
    };
  }

  async getCurrentUser(userId: string) {
    console.log('🔍 Getting current user from token:', userId);
    
    const user = await this.authModel.findById(userId)
      .select('-password -refreshToken -verificationCode')
      .exec();
      
    if (!user) {
      console.log('❌ User not found');
      throw new NotFoundException('User not found');
    }
    
    console.log('✅ Current user retrieved successfully');
    return user;
  }

  async changeName(userId: string, firstName: string, lastName: string) {
    console.log('📝 Changing name for user:', userId);
    
    const user = await this.authModel.findById(userId);
    if (!user) {
      throw new NotFoundException('User not found');
    }

    const oldName = `${user.firstName} ${user.lastName}`;
    
    const updatedUser = await this.authModel.findByIdAndUpdate(
      userId,
      { 
        firstName: firstName.trim(),
        lastName: lastName.trim()
      },
      { new: true }
    ).select('-password -refreshToken -verificationCode');

    if (!updatedUser) {
      throw new NotFoundException('User not found');
    }

    console.log(`✅ Name changed from "${oldName}" to "${firstName} ${lastName}" for user:`, user.email);

    return {
      status: 200,
      message: 'Name updated successfully',
      data: {
        id: updatedUser._id,
        firstName: updatedUser.firstName,
        lastName: updatedUser.lastName,
        email: updatedUser.email
      }
    };
  }

  async createManager(adminId: string, createManagerDto: CreateManagerDto) {
    console.log('🔧 Creating manager user...');

    // Verify admin is super admin
    const admin = await this.authModel.findById(adminId);
    if (!admin || admin.role !== 'admin') {
      console.log('❌ Unauthorized: Only super admin can create managers');
      throw new ForbiddenException('Only super admin can create managers');
    }

    // Check if user already exists
    const existingUser = await this.authModel.findOne({
      email: createManagerDto.email,
    });
    if (existingUser) {
      console.log('❌ User already exists:', createManagerDto.email);
      throw new ConflictException('User with this email already exists');
    }

    // Validate roles (only allow specific roles)
    const allowedRoles = ['withdraw', 'user', 'support', 'broker'];
    const invalidRoles = createManagerDto.roles.filter(role => !allowedRoles.includes(role));
    if (invalidRoles.length > 0) {
      throw new BadRequestException(`Invalid roles: ${invalidRoles.join(', ')}. Allowed roles: ${allowedRoles.join(', ')}`);
    }

    // Hash password
    const saltRounds = 10;
    const hashedPassword = await bcrypt.hash(createManagerDto.password, saltRounds);
    const referralCode = generateVerificationCode();

    // Create manager user
    const managerUser = new this.authModel({
      email: createManagerDto.email,
      password: hashedPassword,
      firstName: createManagerDto.firstName,
      lastName: createManagerDto.lastName,
      role: 'manager',
      roles: createManagerDto.roles,
      status: 'active',
      isEmailVerified: true,
      loginType: 'email',
      referralCode,
      refferedBy: null,
    });

    const result = await managerUser.save();
    console.log('✅ Manager user created successfully:', result._id);

    return {
      message: 'Manager created successfully',
      data: {
        id: result._id,
        email: result.email,
        firstName: result.firstName,
        lastName: result.lastName,
        role: result.role,
        roles: result.roles,
        status: result.status,
      },
    };
  }
}
