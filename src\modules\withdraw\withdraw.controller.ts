import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Request } from '@nestjs/common';
import { WithdrawService } from './withdraw.service';
import { CreateWithdrawDto, UpdateWithdrawStatusDto } from './dto/withdraw.dto';
import { JwtAuthGuard } from '../../Gaurd/jwt-auth.guard';
import { ApiOperation, ApiBearerAuth } from '@nestjs/swagger';

@Controller('withdraw')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class WithdrawController {
  constructor(private readonly withdrawService: WithdrawService) {}

  @Post()
  @ApiOperation({ summary: 'Create withdraw request' })
  create(@Body() createWithdrawDto: CreateWithdrawDto, @Request() req) {
    return this.withdrawService.create(createWithdrawDto, req.user.sub);
  }

  @Get()
  @ApiOperation({ summary: 'Get all withdraw requests (Admin only)' })
  findAll(@Request() req) {
    return this.withdrawService.findAll(req.user.sub);
  }

  @Get('my-withdraws')
  @ApiOperation({ summary: 'Get user withdraw requests' })
  findUserWithdraws(@Request() req) {
    return this.withdrawService.findUserWithdraws(req.user.sub);
  }

  @Get('user/:userId')
  @ApiOperation({ summary: 'Get user withdraw requests by admin' })
  findUserWithdrawsByAdmin(@Param('userId') userId: string, @Request() req) {
    return this.withdrawService.findUserWithdrawsByAdmin(userId, req.user.sub);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get withdraw request by ID' })
  findOne(@Param('id') id: string) {
    return this.withdrawService.findOne(id);
  }

  @Patch(':id/status')
  @ApiOperation({ summary: 'Update withdraw status (Admin only)' })
  updateStatus(@Param('id') id: string, @Body() updateStatusDto: UpdateWithdrawStatusDto, @Request() req) {
    return this.withdrawService.updateStatus(id, updateStatusDto, req.user.sub);
  }

  @Post('process-pending')
  @Roles('withdraw')
  @ApiOperation({ summary: 'Process all pending withdrawals via AirDrop (Admin/Manager with withdraw role only)' })
  processPendingWithdraws(@Request() req) {
    return this.withdrawService.updateStatus(undefined, undefined, req.user.sub);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete withdraw request' })
  remove(@Param('id') id: string) {
    return this.withdrawService.remove(id);
  }

  @Get('admin/user-withdraws/:userId')
  @Roles('withdraw')
  @ApiOperation({ summary: 'Get all withdraws for a specific user (Admin/Manager with withdraw role only)' })
  getAdminUserWithdraws(@Param('userId') userId: string, @Request() req) {
    return this.withdrawService.getAdminUserWithdraws(userId, req.user.sub);
  }
}
