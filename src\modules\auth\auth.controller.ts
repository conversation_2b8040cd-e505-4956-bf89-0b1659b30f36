import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
  Res,
  Put,
  Request,
} from '@nestjs/common';
import { AuthService } from './auth.service';
import {
  CreateAuthDto,
  LoginDto,
  RefreshTokenDto,
  ForgetPasswordDto,
  ResetPasswordDto,
  UpdateEmailDto,
  ChangePasswordDto,
  DeactivateAccountDto,
  ChangeNameDto,
  CreateManagerDto,
} from './dto/auth.dto';
import { CreateIncentiveDto } from './dto/incentive.dto';
import { JwtAuthGuard } from '../../Gaurd/jwt-auth.guard';
import { Public } from '../../Gaurd/public.decorator';
import { CurrentUser } from '../../Gaurd/user.decorator';
import { ApiOperation, ApiBearerAuth } from '@nestjs/swagger';

@Controller('auth')
@UseGuards(JwtAuthGuard)
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Public()
  @Post('register')
  create(@Body() createAuthDto: CreateAuthDto) {
    return this.authService.create(createAuthDto);
  }

  @Public()
  @Post('login')
  login(@Body() loginDto: LoginDto) {
    return this.authService.login(loginDto);
  }

  @Public()
  @Post('admin-login')
  @ApiOperation({ summary: 'Admin login with email and password' })
  adminLogin(@Body() loginDto: LoginDto) {
    return this.authService.adminLogin(loginDto);
  }

  @Public()
  @Post('refresh')
  refreshToken(@Body() refreshTokenDto: RefreshTokenDto) {
    return this.authService.refreshToken(refreshTokenDto.refresh_token);
  }

  @Post('logout')
  logout(@CurrentUser() user: any) {
    return this.authService.logout(user.userId);
  }

  @Get('all-users')
  @ApiBearerAuth('access-token')
  @ApiOperation({ summary: 'Get all users with team count' })
  findAll(@Request() req: any) {
    const userId = req.user.userId;
    return this.authService.findAll(userId);
  }

  @Get('users-by-earnings')
  @ApiBearerAuth('access-token')
  @ApiOperation({ summary: 'Get all users ranked by total trade earnings' })
  findUsersByEarnings(@Request() req: any) {
    const userId = req.user.userId;
    return this.authService.findUsersByEarnings(userId);
  }

  @Public()
  @Get('verify-email')
  async verifyEmail(
    @Query('code') code: string,
    @Query('email') email: string,
    @Res() res: any,
  ) {
    console.log(
      '🔓 Verify email route called with code:',
      code,
      'email:',
      email,
    );
    try {
      const result = await this.authService.verifyEmail(code, email);
      console.log(result);

      return res.redirect(
        `https://trade-reward.vercel.app/verification?token=${encodeURIComponent(result.access_token)}&email=${encodeURIComponent(result.user.email)}&id=${encodeURIComponent(
          result.user.id.toString(),
        )}&role=${encodeURIComponent(result.user.role)}&firstName=${encodeURIComponent(result.user.firstName)}&lastName=${encodeURIComponent(result.user.lastName)}
        }`,
      );
    } catch (error) {
      console.log('❌ Email verification error:', error.message);
      // Redirect to frontend with error
      return res.redirect(
        'https://trade-reward.vercel.app/verification?error=invalid',
      );
    }
  }

  @Public()
  @Get('verify-reset-password')
  async verifyResetPassword(
    @Query('code') code: string,
    @Query('email') email: string,
    @Res() res: any,
  ) {
    console.log(
      '🔓 Verify reset password route called with code:',
      code,
      'email:',
      email,
    );
    try {
      await this.authService.verifyResetPassword(code, email);
      // Redirect to frontend new-password page with code and email as query parameters
      return res.redirect(
        `https://trade-reward.vercel.app/new-password?code=${encodeURIComponent(code)}&email=${encodeURIComponent(email)}`,
      );
    } catch (error) {
      console.log('❌ Reset password verification error:', error.message);
      // Redirect to frontend with error
      return res.redirect(
        'https://trade-reward.vercel.app/new-password?error=invalid',
      );
    }
  }

  @Public()
  @Post('forget-password')
  forgetPassword(@Body() forgetPasswordDto: ForgetPasswordDto) {
    return this.authService.forgetPassword(forgetPasswordDto.email);
  }

  @Public()
  @Post('reset-password')
  resetPassword(@Body() resetPasswordDto: ResetPasswordDto) {
    return this.authService.resetPassword(
      resetPasswordDto.code,
      resetPasswordDto.email,
      resetPasswordDto.password,
    );
  }

  @Put('update-email')
  @ApiOperation({ summary: 'Update email in User' })
  async updateEmail(
    @CurrentUser() user: any,
    @Body() updateEmailDto: UpdateEmailDto,
  ) {
    const userId = user.userId;
    return this.authService.updateEmail(userId, updateEmailDto.email);
  }

  @Put('change-password')
  @ApiBearerAuth('access-token')
  @ApiOperation({ summary: 'Change Password using Old Password' })
  async changePassword(
    @CurrentUser() user: any,
    @Body() dto: ChangePasswordDto,
  ) {
    const userId = user.userId;
    return await this.authService.changePassword(
      userId,
      dto.oldPassword,
      dto.newPassword,
    );
  }

  @Put('deactivate-account')
  @ApiBearerAuth('access-token')
  @ApiOperation({ summary: 'Deactivate User Account' })
  async deactivateAccount(
    @CurrentUser() user: any,
    @Body() dto: DeactivateAccountDto,
  ) {
    const userId = user.userId;
    return await this.authService.deactivateAccount(userId, dto.status);
  }

  @Get('user-referrals/:userId')
  @ApiBearerAuth('access-token')
  @ApiOperation({
    summary:
      'Get all referrals (level 1, 2, 3) for a specific user (Admin only)',
  })
  @UseGuards(JwtAuthGuard)
  async getUserReferrals(@Request() req: any, @Param('userId') userId: string) {
    const adminId = req.user.userId;
    return await this.authService.getUserReferrals(adminId, userId);
  }

  @Post('give-incentive')
  @ApiBearerAuth('access-token')
  @ApiOperation({ summary: 'Give incentives to top ranked users (Admin only)' })
  @UseGuards(JwtAuthGuard)
  async giveIncentiveToTopUsers(
    @Request() req: any,
    @Body() createIncentiveDto: CreateIncentiveDto,
  ) {
    const adminId = req.user.userId;
    return await this.authService.giveIncentiveToTopUsers(
      adminId,
      createIncentiveDto,
    );
  }

  @Get('incentives')
  @ApiBearerAuth('access-token')
  @ApiOperation({ summary: 'Get all incentives (Admin only)' })
  @UseGuards(JwtAuthGuard)
  async getAllIncentives(@Request() req: any) {
    const adminId = req.user.userId;
    return await this.authService.getAllIncentives(adminId);
  }

  @Get('my-incentives')
  @ApiBearerAuth('access-token')
  @ApiOperation({ summary: 'Get user incentives' })
  @UseGuards(JwtAuthGuard)
  async getUserIncentives(@Request() req: any) {
    const userId = req.user.userId;
    return await this.authService.getUserIncentives(userId);
  }

  @Put('lock-unlock/:userId')
  @ApiBearerAuth('access-token')
  @ApiOperation({ summary: 'Lock and Unlock User' })
  @UseGuards(JwtAuthGuard)
  async lockAndUnlockUser(
    @Request() req: any,
    @Param('userId') userId: string,
  ) {
    const adminId = req.user.userId;
    return await this.authService.lockAndUnlockUser(adminId, userId);
  }

  @Get('user-status')
  @ApiBearerAuth('access-token')
  @ApiOperation({ summary: 'Get user status (check if user is locked or not)' })
  @UseGuards(JwtAuthGuard)
  async getUserStatus(@Request() req: any) {
    const userId = req.user.userId;
    return await this.authService.getUserStatus(userId);
  }

  @Get('newUsers')
  @ApiBearerAuth('access-token')
  @ApiOperation({
    summary: 'Get all users registered in the last 7 days',
  })
  @UseGuards(JwtAuthGuard)
  async getLast7DaysUsers(@Request() req: any) {
    const adminId = req.user.userId;
    return await this.authService.getLast7DaysUsers(adminId);
  }

  @Get('me')
  @ApiBearerAuth('access-token')
  @ApiOperation({ summary: 'Get current user information from token' })
  @UseGuards(JwtAuthGuard)
  async getCurrentUser(@Request() req: any) {
    const userId = req.user.userId;
    return this.authService.getCurrentUser(userId);
  }

  @Put('change-name')
  @ApiBearerAuth('access-token')
  @ApiOperation({ summary: 'Change user name' })
  @UseGuards(JwtAuthGuard)
  async changeName(
    @Request() req: any,
    @Body() changeNameDto: ChangeNameDto,
  ) {
    const userId = req.user.userId;
    return this.authService.changeName(userId, changeNameDto.firstName, changeNameDto.lastName);
  }

  @Post('create-manager')
  @ApiBearerAuth('access-token')
  @ApiOperation({ summary: 'Create manager user with assigned roles (Super Admin only)' })
  @UseGuards(JwtAuthGuard)
  async createManager(
    @Request() req: any,
    @Body() createManagerDto: CreateManagerDto,
  ) {
    const adminId = req.user.userId;
    return this.authService.createManager(adminId, createManagerDto);
  }

  @Get('managers')
  @ApiBearerAuth('access-token')
  @ApiOperation({ summary: 'Get all managers (Super Admin only)' })
  @UseGuards(JwtAuthGuard)
  async getManagers(@Request() req: any) {
    const adminId = req.user.userId;
    return this.authService.getManagers(adminId);
  }
}
